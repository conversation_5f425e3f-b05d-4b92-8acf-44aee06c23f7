<script setup lang="ts">
import { useEstate } from "./utils/hook";
import { onMounted, reactive, ref } from "vue";
import { getDict, showDictValue, showDict } from "@/utils/dict";
import { houseApi } from "@/api/housing/house";
import { deviceDetection } from "@pureadmin/utils";
import { useHousingHooks } from "@/views/housing/hooks";

defineOptions({
  name: "BuildingMap"
});

const form = reactive({
  xq_id: null,
  ld_id: null,
  fwzt: null,
  limit: 999
});

const dataList = ref([]);
const buildingTableData = ref<BuildingTableData>({
  floors: [],
  units: [],
  houseMatrix: {}
});
const loading = ref(true);
const tableRef = ref();
const { initToBuildingMap, getParameter } = useEstate(tableRef);
const { buildingOptions, getBuilding } = useHousingHooks();

initToBuildingMap("query");

async function onSearch() {
  loading.value = true;
  const { data } = await houseApi.index(form);
  buildingTableData.value = transformHouseData(data.data);

  // 调试信息
  console.log('原始数据:', data.data);
  console.log('转换后的表格数据:', buildingTableData.value);

  setTimeout(() => {
    loading.value = false;
  }, 500);
}

onMounted(async () => {
  form.xq_id = getParameter.xq_id;
  await getBuilding(getParameter.xq_id);

  if (buildingOptions.value && buildingOptions.value.length > 0) {
    checkedBuilding.value = buildingOptions.value[0];
    form.ld_id = buildingOptions.value[0].id;
  }
  onSearch();
});

interface Building {
  id: number;
  xqmc: string;
  ldmc: string;
}

const checkedBuilding = ref<Building>({ id: 0, ldmc: "", xqmc: "" });
const { fwzt: fwztOptions } = getDict("fwzt");

function buildingChange(obj) {
  checkedBuilding.value = obj;
  if (obj.id) {
    form.ld_id = obj.id;
    onSearch();
  }
}

function buildingZtChange(zt = null) {
  form.fwzt = zt;
  onSearch();
}

interface House {
  id: number;
  xq_id: number;
  ld_id: number;
  xqmc: string;
  ldmc: string;
  dy: string;
  lc: number;
  xh: number;
  sh: string;
  hx: number;
  jzmj: string;
  fwxz: number;
  fwzt: number;
  bz: string;
}

// 楼盘表格数据结构
interface BuildingTableData {
  floors: number[]; // 楼层数组，从高到低排序
  units: number[]; // 单元数组
  houseMatrix: { [key: string]: House[] }; // 房屋矩阵，key为"单元-楼层"，值为该位置的房屋数组
}

/**
 * 将房屋数据转换为表格结构
 * @param houses 房屋数据数组
 */
function transformHouseData(houses: House[]): BuildingTableData {
  const unitsSet = new Set<number>();
  const floorsSet = new Set<number>();
  const houseMatrix: { [key: string]: House[] } = {};

  // 收集所有单元和楼层，并建立房屋矩阵
  houses.forEach(house => {
    const unit = parseInt(house.dy);
    const floor = house.lc;

    unitsSet.add(unit);
    floorsSet.add(floor);

    // 使用单元-楼层作为key
    const key = `${unit}-${floor}`;
    if (!houseMatrix[key]) {
      houseMatrix[key] = [];
    }
    houseMatrix[key].push(house);
  });

  // 转换为数组并排序
  const units = Array.from(unitsSet).sort((a, b) => a - b);
  const floors = Array.from(floorsSet).sort((a, b) => b - a); // 楼层从高到低

  // 创建完整的矩阵，包含空位置
  const completeMatrix: { [key: string]: House[] } = {};
  units.forEach(unit => {
    floors.forEach(floor => {
      const key = `${unit}-${floor}`;
      completeMatrix[key] = houseMatrix[key] || [];
    });
  });

  return {
    floors,
    units,
    houseMatrix: completeMatrix
  };
}

/**
 * 根据单元和房屋状态获取背景颜色
 * @param unit 单元号
 * @param fwzt 房屋状态
 */
function getHouseStatusColor(unit: number, fwzt: number): string {
  // 基础颜色根据单元确定
  let baseColor = '';
  switch (unit) {
    case 1: baseColor = '#DAA520'; break; // 1单元 - 金黄色
    case 2: baseColor = '#FF8C00'; break; // 2单元 - 橙色
    case 3: baseColor = '#FFA07A'; break; // 3单元 - 浅橙色
    case 4: baseColor = '#90EE90'; break; // 4单元 - 浅绿色
    default: baseColor = '#87CEEB'; break; // 默认 - 天蓝色
  }

  // 根据房屋状态调整透明度或亮度
  // 这里可以根据实际的fwzt值来调整
  return baseColor;
}

/**
 * 房屋点击事件处理
 * @param house 房屋数据
 */
function onHouseClick(house: House) {
  console.log('点击房屋:', house);
  // 这里可以添加房屋详情弹窗或其他交互逻辑
  // 例如：显示房屋详细信息、编辑房屋信息等
}
</script>

<template>
  <div
    :class="['flex', 'justify-between', deviceDetection() && 'flex-wrap']"
    class="el-text"
  >
    <div
      class="h-full bg-bg_color overflow-auto pl-4 pt-[12px]"
      :class="['mr-2', deviceDetection() ? 'w-full' : 'min-w-[200px]']"
      :style="{ minHeight: `calc(100vh - 140px)` }"
    >
      <div>小区名称：{{ checkedBuilding.xqmc }}</div>
      <div>楼栋名称：{{ checkedBuilding.ldmc }}栋</div>
    </div>
    <div
      class="h-full overflow-auto el-text"
      :class="[deviceDetection() ? ['w-full', 'mt-2'] : 'w-[calc(100%-200px)]']"
      :style="{ minHeight: `calc(100vh - 140px)` }"
    >
      <div class="bg-bg_color w-[99/100] pl-4 pt-[12px] h-[62px] overflow-auto">
        <div class="ld">
          楼栋：
          <el-button
            v-for="(item, index) in buildingOptions"
            :key="index"
            type="primary"
            link
            class="ml-5"
            @click="buildingChange(item)"
          >
            {{ item.ldmc }}栋
          </el-button>
        </div>
        <div class="zt">
          状态：
          <el-button
            type="primary"
            link
            class="ml-5"
            @click="buildingZtChange()"
          >
            全部
          </el-button>
          <el-button
            v-for="(item, index) in fwztOptions.dictData"
            :key="index"
            :type="showDict('fwzt', item.value).type"
            link
            class="ml-5"
            @click="buildingZtChange(item.value)"
          >
            {{ item.label }}
          </el-button>
        </div>
      </div>
      <div
        class="h-full bg-bg_color mt-2"
        :style="{ minHeight: `calc(100vh - 210px)` }"
      >
        <el-scrollbar max-height="730px">
          <!-- 楼盘表格布局 -->
          <div class="building-table" v-if="buildingTableData.units.length > 0">
            <!-- 表头 - 单元 -->
            <div class="table-header">
              <div class="floor-label"></div>
              <div
                v-for="unit in buildingTableData.units"
                :key="unit"
                class="unit-header"
              >
                {{ unit }}单元
              </div>
            </div>

            <!-- 表格内容 -->
            <div
              v-for="floor in buildingTableData.floors"
              :key="floor"
              class="table-row"
            >
              <!-- 楼层标签 -->
              <div class="floor-label">{{ floor }}层</div>

              <!-- 房屋单元格 -->
              <div
                v-for="unit in buildingTableData.units"
                :key="`${unit}-${floor}`"
                class="house-cell"
              >
                <!-- 如果该位置有房屋 -->
                <div
                  v-if="buildingTableData.houseMatrix[`${unit}-${floor}`] && buildingTableData.houseMatrix[`${unit}-${floor}`].length > 0"
                  class="house-group"
                  :data-room-count="buildingTableData.houseMatrix[`${unit}-${floor}`].length"
                >
                  <div
                    v-for="house in buildingTableData.houseMatrix[`${unit}-${floor}`]"
                    :key="house.id"
                    class="house-item"
                    :style="{
                      backgroundColor: getHouseStatusColor(unit, house.fwzt)
                    }"
                    @click="onHouseClick(house)"
                  >
                    <div class="house-number">
                      {{ house.sh }}
                    </div>
                    <div class="house-info">
                      <div class="house-area">
                        {{ house.jzmj }}m²
                      </div>
                      <div class="house-type">
                        {{ showDictValue("huxing", house.hx) }}
                      </div>
                    </div>
                    <div class="house-status">
                      {{ showDictValue("fwzt", house.fwzt) }}
                    </div>
                  </div>
                </div>
                <!-- 如果该位置没有房屋 -->
                <div v-else class="empty-cell"></div>
              </div>
            </div>
          </div>

          <!-- 无数据提示 -->
          <div v-else class="no-data">
            <el-empty description="暂无房屋数据" />
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

:deep(.el-button:focus-visible) {
  outline: none;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.ld,
.zt {
  display: flex;
}

// 楼盘表格布局样式
.building-table {
  padding: 20px;

  .table-header {
    display: flex;
    margin-bottom: 2px;

    .floor-label {
      width: 80px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-color-primary-light-8);
      border: 1px solid var(--el-border-color);
      font-weight: bold;
      font-size: 14px;
    }

    .unit-header {
      flex: 1;
      min-width: 120px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-color-primary);
      color: white;
      border: 1px solid var(--el-border-color);
      font-weight: bold;
      font-size: 14px;
      margin-left: 2px;
    }
  }

  .table-row {
    display: flex;
    margin-bottom: 2px;

    .floor-label {
      width: 80px;
      height: 120px; // 与房屋单元格高度匹配
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-color-primary-light-8);
      border: 1px solid var(--el-border-color);
      font-weight: bold;
      font-size: 14px;
    }

    .house-cell {
      flex: 1;
      min-width: 200px; // 增加最小宽度以容纳更多房间
      height: 120px; // 增加高度以容纳更多房间
      margin-left: 2px;
      border: 1px solid var(--el-border-color);

      .house-group {
        width: 100%;
        height: 100%;
        display: grid;
        gap: 2px;
        padding: 2px;

        // 根据房间数量动态调整网格布局
        &[data-room-count="1"] {
          grid-template-columns: 1fr;
          grid-template-rows: 1fr;
        }

        &[data-room-count="2"] {
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr;
        }

        &[data-room-count="3"] {
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;

          .house-item:first-child {
            grid-column: 1 / -1;
          }
        }

        &[data-room-count="4"] {
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;
        }

        &[data-room-count="5"],
        &[data-room-count="6"] {
          grid-template-columns: 1fr 1fr 1fr;
          grid-template-rows: 1fr 1fr;
        }

        &[data-room-count="7"],
        &[data-room-count="8"] {
          grid-template-columns: 1fr 1fr 1fr 1fr;
          grid-template-rows: 1fr 1fr;
        }

        // 超过8个房间时使用更多行
        &:not([data-room-count]) {
          grid-template-columns: repeat(4, 1fr);
          grid-auto-rows: minmax(25px, 1fr);
        }
      }

      .house-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 3px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 3px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        min-height: 0; // 允许flex项目缩小
        overflow: hidden;

        &:hover {
          transform: scale(1.08);
          box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
          z-index: 10;
          position: relative;
        }

        .house-number {
          font-weight: bold;
          font-size: 11px;
          color: #333;
          text-align: center;
          line-height: 1.2;
          margin-bottom: 2px;
        }

        .house-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 9px;
          color: #666;
          line-height: 1.1;
          margin-bottom: 2px;

          .house-area {
            font-weight: 500;
          }

          .house-type {
            font-size: 8px;
          }
        }

        .house-status {
          font-size: 8px;
          text-align: center;
          color: #333;
          font-weight: 500;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 2px;
          padding: 1px 2px;
          line-height: 1.1;
        }
      }

      .empty-cell {
        width: 100%;
        height: 100%;
        background-color: var(--el-color-info-light-9);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--el-text-color-placeholder);
        font-size: 12px;
      }
    }
  }
}

.no-data {
  padding: 40px;
  text-align: center;
}
</style>
